<?php 
 
$id="x8nlwwo"; 
#url a solicitar. 
$url= "https://www.dailymotion.com/player/metadata/video/".$id."?embedder=https%3A%2F%2Fgo.medcom.com.pa%2F&geo=1&player-id=xhq0f&locale=es&dmV1st=e5290302-bd86-4dcc-9733-92926e698c82&dmTs=139192&is_native_app=0&app=mark.va.gp&dmSharingUrlLocation=https%3A%2F%2Fgo.medcom.com.pa%2Ftelemetro".$id; 
#obtener contenido y filtrar contenido. 
$tmp= file_get_contents($url); 
$video_info = $tmp; 
preg_match('/,"qualities":(.+),"reporting"/', $video_info, $match); 
#parsear expresiones irregulares. 
$rep= str_replace("\/","/", $match);
#buscar enlaces en contenido. 
$regex = '/https?\:\/\/[^\" ]+/i'; 
preg_match($regex, $rep[0], $matches); 
#obtener contenido de enlace y mostrar. 
$view= file_get_contents($matches[0]); 
#esto es para gestionar la calidad. 

$sd=isset($_GET["calidad"]) ? $_GET["calidad"] : null; 
if(!$sd){ 
    print $view; 
header("Content-Type: application/x-mpegURL"); 
header("Content-disposition: inline; filename=colorv.m3u8"); 
}else{ 
    $regexs = '!https?://\S+!'; 
    preg_match_all($regexs, $view, $links); 
switch($hd){ 
    case 480: 
    header("Location: ".$links[0][0]); break; 
    case 240: 
    header("Location: ".$links[0][2]); break; 
    case 380: 
    header("Location: ".$links[0][4]); break; 
    case 720: 
    header("Location: ".$links[0][6]); break; 
    case 1080: 
    header("Location: ".$links[0][8]); break; 
} 
}
