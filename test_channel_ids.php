<?php
/**
 * Script para probar diferentes IDs de canales de MEDCOM
 */

// Configuración de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

function testChannelId($id, $channel_name) {
    echo "<h3>Probando {$channel_name} con ID: {$id}</h3>\n";
    
    $url = "https://www.dailymotion.com/player/metadata/video/{$id}?embedder=https%3A%2F%2Fgo.medcom.com.pa%2F&geo=1&player-id=xhq0f&locale=es&dmV1st=e5290302-bd86-4dcc-9733-92926e698c82&dmTs=139192&is_native_app=0&app=mark.va.gp&dmSharingUrlLocation=https%3A%2F%2Fgo.medcom.com.pa%2F{$channel_name}{$id}";
    
    echo "URL: <a href='{$url}' target='_blank'>{$url}</a><br>\n";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]
    ]);
    
    $result = @file_get_contents($url, false, $context);
    
    if ($result === false) {
        echo "<span style='color: red;'>❌ ERROR: No se pudo conectar</span><br>\n";
        return false;
    }
    
    if (empty($result)) {
        echo "<span style='color: red;'>❌ ERROR: Respuesta vacía</span><br>\n";
        return false;
    }
    
    echo "<span style='color: green;'>✅ Conexión exitosa</span> - Tamaño: " . strlen($result) . " bytes<br>\n";
    
    // Verificar si contiene datos de video
    if (strpos($result, '"qualities"') !== false) {
        echo "<span style='color: green;'>✅ Contiene información de calidades</span><br>\n";
        
        // Intentar extraer URL M3U8
        if (preg_match('/,"qualities":(.+?),"reporting"/', $result, $match)) {
            $rep = str_replace("\/", "/", $match[1]);
            if (preg_match('/https?:\/\/[^\s"]+\.m3u8[^\s"]*/i', $rep, $matches)) {
                echo "<span style='color: green;'>✅ URL M3U8 encontrada:</span> {$matches[0]}<br>\n";
                return true;
            } else {
                echo "<span style='color: orange;'>⚠️ No se encontró URL M3U8</span><br>\n";
            }
        } else {
            echo "<span style='color: orange;'>⚠️ No se pudo extraer sección de calidades</span><br>\n";
        }
    } else {
        echo "<span style='color: red;'>❌ No contiene información de calidades</span><br>\n";
        
        // Mostrar parte del contenido para debugging
        echo "<details><summary>Ver contenido (primeros 500 caracteres)</summary><pre>";
        echo htmlspecialchars(substr($result, 0, 500));
        echo "</pre></details><br>\n";
    }
    
    echo "<hr>\n";
    return false;
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test de IDs de Canales MEDCOM</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h3 { color: #666; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>Test de IDs de Canales MEDCOM</h1>
    <p>Probando diferentes IDs para encontrar los correctos...</p>

<?php

// IDs actuales que estamos usando
$current_ids = [
    'oyetv' => 'x8odgha',
    'rpc' => 'x8lwrcd', 
    'telemetro' => 'x8nlwwo'
];

echo "<h2>IDs Actuales</h2>\n";
foreach ($current_ids as $channel => $id) {
    testChannelId($id, $channel);
}

// IDs alternativos para probar (basados en patrones comunes de Dailymotion)
$alternative_ids = [
    'telemetro' => [
        'x8nlwwo', // ID actual
        'x7nlwwo', // Variación
        'x9nlwwo', // Variación
        'x8mlwwo', // Variación
        'x8nkwwo', // Variación
        'x8nlvwo', // Variación
        'x8nlwvo', // Variación
    ],
    'oyetv' => [
        'x8odgha', // ID actual
        'x7odgha', // Variación
        'x9odgha', // Variación
    ],
    'rpc' => [
        'x8lwrcd', // ID actual
        'x7lwrcd', // Variación
        'x9lwrcd', // Variación
    ]
];

echo "<h2>Probando IDs Alternativos</h2>\n";
foreach ($alternative_ids as $channel => $ids) {
    echo "<h3>Canal: {$channel}</h3>\n";
    foreach ($ids as $id) {
        testChannelId($id, $channel);
    }
}

?>

<h2>Instrucciones</h2>
<ol>
    <li>Revisa los resultados arriba para ver qué IDs funcionan</li>
    <li>Los IDs que muestren "✅ URL M3U8 encontrada" son los correctos</li>
    <li>Si ningún ID funciona, puede ser que los canales hayan cambiado sus IDs</li>
    <li>También puedes probar accediendo directamente a las URLs mostradas</li>
</ol>

<h2>Cómo encontrar IDs actualizados</h2>
<ol>
    <li>Ve a <a href="https://go.medcom.com.pa/" target="_blank">go.medcom.com.pa</a></li>
    <li>Abre las herramientas de desarrollador (F12)</li>
    <li>Ve a la pestaña Network</li>
    <li>Reproduce un canal</li>
    <li>Busca requests a dailymotion.com que contengan "/player/metadata/video/"</li>
    <li>El ID estará en la URL después de "/video/"</li>
</ol>

</body>
</html>
