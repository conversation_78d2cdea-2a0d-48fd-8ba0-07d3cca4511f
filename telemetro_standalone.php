<?php
/**
 * Telemetro Stream Proxy - Versión Standalone
 * No requiere archivos externos, todo incluido
 */

// Configuración de errores
error_reporting(E_ALL);
ini_set('display_errors', 0);

/**
 * Función para realizar peticiones HTTP con reintentos y timeout
 */
function fetchWithRetry($url, $maxRetries = 5, $timeout = 15) {
    $context = stream_context_create([
        'http' => [
            'timeout' => $timeout,
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: */*',
                'Connection: keep-alive',
                'Accept-Language: es-ES,es;q=0.9,en;q=0.8'
            ]
        ]
    ]);
    
    for ($i = 0; $i < $maxRetries; $i++) {
        $result = @file_get_contents($url, false, $context);
        
        if ($result !== false && !empty($result)) {
            return $result;
        }
        
        // Esperar antes del siguiente intento (backoff exponencial)
        if ($i < $maxRetries - 1) {
            sleep(pow(2, $i)); // 1s, 2s, 4s, 8s, 16s
        }
    }
    
    return false;
}

/**
 * Función para extraer URLs de video
 */
function extractVideoUrls($id, $channel_name) {
    // Construir URL de metadata
    $url = "https://www.dailymotion.com/player/metadata/video/{$id}?embedder=https%3A%2F%2Fgo.medcom.com.pa%2F&geo=1&player-id=xhq0f&locale=es&dmV1st=e5290302-bd86-4dcc-9733-92926e698c82&dmTs=139192&is_native_app=0&app=mark.va.gp&dmSharingUrlLocation=https%3A%2F%2Fgo.medcom.com.pa%2F{$channel_name}{$id}";
    
    // Obtener contenido con reintentos
    $video_info = fetchWithRetry($url);
    if ($video_info === false) {
        return false;
    }
    
    // Validar que el contenido tenga el formato esperado
    if (strpos($video_info, '"qualities"') === false) {
        return false;
    }
    
    // Filtrar contenido con validación mejorada
    if (!preg_match('/,"qualities":(.+?),"reporting"/', $video_info, $match)) {
        // Intentar patrón alternativo
        if (!preg_match('/"qualities":(.+?),"/', $video_info, $match)) {
            return false;
        }
    }
    
    // Parsear expresiones regulares
    $rep = str_replace("\/", "/", $match[1]);
    
    // Buscar enlaces en contenido con regex más específica
    $regex = '/https?:\/\/[^\s"]+\.m3u8[^\s"]*/i';
    if (!preg_match($regex, $rep, $matches)) {
        // Fallback a regex más general
        $regex_general = '/https?:\/\/[^"\s]+/i';
        if (!preg_match($regex_general, $rep, $matches)) {
            return false;
        }
    }
    
    // Obtener contenido del enlace M3U8
    $m3u8_content = fetchWithRetry($matches[0]);
    if ($m3u8_content === false) {
        return false;
    }
    
    return $m3u8_content;
}

/**
 * Función para extraer enlaces de calidades específicas
 */
function extractQualityLinks($m3u8_content) {
    $regex = '!https?://[^\s]+!';
    if (!preg_match_all($regex, $m3u8_content, $links)) {
        return false;
    }
    return $links[0];
}

/**
 * Enviar headers apropiados para M3U8
 */
function sendM3U8Headers($filename) {
    header("Content-Type: application/x-mpegURL");
    header("Content-disposition: inline; filename={$filename}");
    header("Cache-Control: no-cache, no-store, must-revalidate");
    header("Pragma: no-cache");
    header("Expires: 0");
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type");
}

/**
 * Enviar respuesta de error
 */
function sendError($code, $message) {
    http_response_code($code);
    header('Content-Type: text/plain; charset=utf-8');
    echo $message;
    exit;
}

// Configuración del canal
$id = "x8nlwwo";
$channel_name = "telemetro";

// Procesar la solicitud
try {
    $m3u8_content = extractVideoUrls($id, $channel_name);
    
    if ($m3u8_content === false) {
        sendError(503, "Error: No se pudo obtener el contenido del stream de Telemetro. Intente nuevamente en unos momentos.");
    }
    
    $quality = isset($_GET["calidad"]) ? intval($_GET["calidad"]) : null;
    
    if (!$quality) {
        // Devolver playlist completa
        sendM3U8Headers("telemetro.m3u8");
        echo $m3u8_content;
    } else {
        // Redirigir a calidad específica
        $links = extractQualityLinks($m3u8_content);
        
        if ($links === false) {
            sendError(503, "Error: No se pudieron extraer los enlaces de calidad.");
        }
        
        $quality_map = [
            240 => 2,
            380 => 4, 
            480 => 0,
            720 => 6,
            1080 => 8
        ];
        
        if (isset($quality_map[$quality]) && isset($links[$quality_map[$quality]])) {
            header("Location: " . $links[$quality_map[$quality]]);
        } else {
            // Fallback a la primera calidad disponible
            if (!empty($links)) {
                header("Location: " . $links[0]);
            } else {
                sendError(503, "Error: No hay enlaces de calidad disponibles.");
            }
        }
    }
    
} catch (Exception $e) {
    sendError(500, "Error interno del servidor. Intente nuevamente.");
}
?>
