<?php
/**
 * Telemetro Stream Proxy - Versión de Diagnóstico
 * Esta versión incluye debugging detallado para identificar problemas
 */

// Configuración de errores para debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Función para mostrar debug info
function debugLog($message) {
    echo "DEBUG: " . $message . "\n";
    error_log("TELEMETRO DEBUG: " . $message);
}

// Función para realizar peticiones HTTP con debugging
function fetchWithRetryDebug($url, $maxRetries = 3, $timeout = 10) {
    debugLog("Intentando conectar a: " . $url);
    
    $context = stream_context_create([
        'http' => [
            'timeout' => $timeout,
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept: */*',
                'Connection: keep-alive'
            ]
        ]
    ]);
    
    for ($i = 0; $i < $maxRetries; $i++) {
        debugLog("Intento " . ($i + 1) . " de " . $maxRetries);
        
        $result = @file_get_contents($url, false, $context);
        
        if ($result !== false && !empty($result)) {
            debugLog("Éxito! Datos recibidos: " . strlen($result) . " bytes");
            return $result;
        }
        
        debugLog("Intento " . ($i + 1) . " falló");
        
        // Esperar antes del siguiente intento
        if ($i < $maxRetries - 1) {
            $wait = pow(2, $i);
            debugLog("Esperando " . $wait . " segundos antes del siguiente intento");
            sleep($wait);
        }
    }
    
    debugLog("TODOS LOS INTENTOS FALLARON");
    return false;
}

// Configuración del canal
$id = "x8nlwwo";
$channel_name = "telemetro";

debugLog("=== INICIANDO DIAGNÓSTICO PARA TELEMETRO ===");
debugLog("ID del canal: " . $id);
debugLog("Nombre del canal: " . $channel_name);

// Construir URL de metadata
$url = "https://www.dailymotion.com/player/metadata/video/{$id}?embedder=https%3A%2F%2Fgo.medcom.com.pa%2F&geo=1&player-id=xhq0f&locale=es&dmV1st=e5290302-bd86-4dcc-9733-92926e698c82&dmTs=139192&is_native_app=0&app=mark.va.gp&dmSharingUrlLocation=https%3A%2F%2Fgo.medcom.com.pa%2F{$channel_name}{$id}";

debugLog("URL construida: " . $url);

// Paso 1: Obtener metadata
debugLog("=== PASO 1: OBTENIENDO METADATA ===");
$video_info = fetchWithRetryDebug($url, 3, 15);

if ($video_info === false) {
    debugLog("ERROR: No se pudo obtener metadata");
    http_response_code(503);
    header('Content-Type: text/plain');
    echo "Error: No se pudo conectar a Dailymotion para obtener metadata del canal Telemetro.";
    exit;
}

debugLog("Metadata obtenida exitosamente");
debugLog("Primeros 200 caracteres: " . substr($video_info, 0, 200));

// Paso 2: Buscar sección de calidades
debugLog("=== PASO 2: BUSCANDO SECCIÓN DE CALIDADES ===");

if (strpos($video_info, '"qualities"') === false) {
    debugLog("ERROR: No se encontró la palabra 'qualities' en la respuesta");
    debugLog("Contenido completo de la respuesta:");
    debugLog($video_info);
    http_response_code(503);
    header('Content-Type: text/plain');
    echo "Error: Formato de respuesta inesperado de Dailymotion.";
    exit;
}

if (!preg_match('/,"qualities":(.+?),"reporting"/', $video_info, $match)) {
    debugLog("ERROR: No se pudo extraer la sección de calidades con regex");
    debugLog("Buscando patrones alternativos...");
    
    // Intentar patrones alternativos
    if (preg_match('/"qualities":(.+?),"/', $video_info, $match)) {
        debugLog("Encontrado patrón alternativo");
    } else {
        debugLog("No se encontró ningún patrón de calidades");
        http_response_code(503);
        header('Content-Type: text/plain');
        echo "Error: No se pudo extraer información de calidades.";
        exit;
    }
}

debugLog("Sección de calidades extraída: " . substr($match[1], 0, 100));

// Paso 3: Buscar URL M3U8
debugLog("=== PASO 3: BUSCANDO URL M3U8 ===");

$rep = str_replace("\/", "/", $match[1]);
debugLog("Después de reemplazar barras: " . substr($rep, 0, 100));

$regex = '/https?:\/\/[^\s"]+\.m3u8[^\s"]*/i';
if (!preg_match($regex, $rep, $matches)) {
    debugLog("ERROR: No se encontró URL M3U8 con regex específica");
    debugLog("Intentando regex más general...");
    
    $regex_general = '/https?:\/\/[^"\s]+/i';
    if (preg_match($regex_general, $rep, $matches)) {
        debugLog("Encontrada URL con regex general: " . $matches[0]);
    } else {
        debugLog("No se encontró ninguna URL");
        http_response_code(503);
        header('Content-Type: text/plain');
        echo "Error: No se encontró URL del stream.";
        exit;
    }
}

$m3u8_url = $matches[0];
debugLog("URL M3U8 encontrada: " . $m3u8_url);

// Paso 4: Obtener contenido M3U8
debugLog("=== PASO 4: OBTENIENDO CONTENIDO M3U8 ===");
$m3u8_content = fetchWithRetryDebug($m3u8_url, 3, 15);

if ($m3u8_content === false) {
    debugLog("ERROR: No se pudo obtener contenido M3U8");
    http_response_code(503);
    header('Content-Type: text/plain');
    echo "Error: No se pudo obtener el contenido del stream M3U8.";
    exit;
}

debugLog("Contenido M3U8 obtenido exitosamente");
debugLog("Primeras 10 líneas del M3U8:");
$lines = explode("\n", $m3u8_content);
for ($i = 0; $i < min(10, count($lines)); $i++) {
    debugLog("Línea " . ($i+1) . ": " . $lines[$i]);
}

// Paso 5: Procesar solicitud de calidad
debugLog("=== PASO 5: PROCESANDO SOLICITUD ===");

$quality = isset($_GET["calidad"]) ? intval($_GET["calidad"]) : null;
debugLog("Calidad solicitada: " . ($quality ? $quality : "ninguna (playlist completa)"));

if (!$quality) {
    // Devolver playlist completa
    debugLog("Devolviendo playlist completa");
    header("Content-Type: application/x-mpegURL");
    header("Content-disposition: inline; filename=telemetro.m3u8");
    header("Cache-Control: no-cache, no-store, must-revalidate");
    header("Pragma: no-cache");
    header("Expires: 0");
    echo $m3u8_content;
} else {
    // Extraer enlaces de calidades específicas
    debugLog("Extrayendo enlaces de calidades específicas");
    
    $regex_links = '!https?://[^\s]+!';
    if (!preg_match_all($regex_links, $m3u8_content, $links)) {
        debugLog("ERROR: No se pudieron extraer enlaces de calidad");
        http_response_code(503);
        header('Content-Type: text/plain');
        echo "Error: No se pudieron extraer los enlaces de calidad.";
        exit;
    }
    
    debugLog("Enlaces encontrados: " . count($links[0]));
    for ($i = 0; $i < count($links[0]); $i++) {
        debugLog("Enlace " . $i . ": " . $links[0][$i]);
    }
    
    $quality_map = [
        240 => 2,
        380 => 4, 
        480 => 0,
        720 => 6,
        1080 => 8
    ];
    
    if (isset($quality_map[$quality]) && isset($links[0][$quality_map[$quality]])) {
        $redirect_url = $links[0][$quality_map[$quality]];
        debugLog("Redirigiendo a calidad " . $quality . ": " . $redirect_url);
        header("Location: " . $redirect_url);
    } else {
        // Fallback a la primera calidad disponible
        $fallback_url = $links[0][0];
        debugLog("Calidad no disponible, usando fallback: " . $fallback_url);
        header("Location: " . $fallback_url);
    }
}

debugLog("=== DIAGNÓSTICO COMPLETADO ===");
?>
