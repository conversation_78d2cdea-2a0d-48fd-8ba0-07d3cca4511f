<?php
/**
 * RPC Stream Proxy
 * Canal: RPC (MEDCOM)
 * Versión mejorada con manejo robusto de errores y reconexión automática
 */

// Configuración de errores
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Incluir biblioteca común
require_once 'stream_helper.php';

// Configuración del canal
$id = "x8lwrcd";
$channel_name = "rpc";

// Crear instancia del helper con configuración optimizada
$streamHelper = new StreamHelper(
    $maxRetries = 5,    // Más reintentos para mayor robustez
    $timeout = 15       // Timeout más largo para conexiones lentas
);

// Procesar la solicitud
$streamHelper->processStreamRequest($id, $channel_name);