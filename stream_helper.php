<?php
/**
 * Stream Helper - Biblioteca común para extracción de streams de Dailymotion
 * Mejora la robustez y manejo de errores para los canales de MEDCOM
 */

class StreamHelper {
    
    private $maxRetries;
    private $timeout;
    private $userAgent;
    
    public function __construct($maxRetries = 3, $timeout = 10) {
        $this->maxRetries = $maxRetries;
        $this->timeout = $timeout;
        $this->userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    }
    
    /**
     * Realizar peticiones HTTP con reintentos y timeout
     */
    public function fetchWithRetry($url) {
        $context = stream_context_create([
            'http' => [
                'timeout' => $this->timeout,
                'method' => 'GET',
                'header' => [
                    "User-Agent: {$this->userAgent}",
                    'Accept: */*',
                    'Connection: keep-alive',
                    'Accept-Language: es-ES,es;q=0.9,en;q=0.8'
                ]
            ]
        ]);
        
        for ($i = 0; $i < $this->maxRetries; $i++) {
            $result = @file_get_contents($url, false, $context);
            
            if ($result !== false && !empty($result)) {
                return $result;
            }
            
            // Log del intento fallido (opcional)
            error_log("Intento " . ($i + 1) . " fallido para URL: $url");
            
            // Esperar antes del siguiente intento (backoff exponencial)
            if ($i < $this->maxRetries - 1) {
                sleep(pow(2, $i)); // 1s, 2s, 4s...
            }
        }
        
        error_log("Todos los intentos fallaron para URL: $url");
        return false;
    }
    
    /**
     * Extraer URLs de video desde Dailymotion
     */
    public function extractVideoUrls($id, $channel_name) {
        // Construir URL de metadata
        $url = $this->buildMetadataUrl($id, $channel_name);
        
        // Obtener contenido con reintentos
        $video_info = $this->fetchWithRetry($url);
        if ($video_info === false) {
            return false;
        }
        
        // Validar que el contenido tenga el formato esperado
        if (strpos($video_info, '"qualities"') === false) {
            error_log("Formato de respuesta inesperado para canal: $channel_name");
            return false;
        }
        
        // Filtrar contenido con validación mejorada
        if (!preg_match('/,"qualities":(.+?),"reporting"/', $video_info, $match)) {
            error_log("No se encontró sección de calidades para canal: $channel_name");
            return false;
        }
        
        // Parsear expresiones regulares
        $rep = str_replace("\/", "/", $match[1]);
        
        // Buscar enlaces en contenido con regex más específica
        $regex = '/https?:\/\/[^\s"]+\.m3u8[^\s"]*/i';
        if (!preg_match($regex, $rep, $matches)) {
            error_log("No se encontró URL M3U8 para canal: $channel_name");
            return false;
        }
        
        // Obtener contenido del enlace M3U8
        $m3u8_content = $this->fetchWithRetry($matches[0]);
        if ($m3u8_content === false) {
            error_log("No se pudo obtener contenido M3U8 para canal: $channel_name");
            return false;
        }
        
        return $m3u8_content;
    }
    
    /**
     * Construir URL de metadata de Dailymotion
     */
    private function buildMetadataUrl($id, $channel_name) {
        $base_url = "https://www.dailymotion.com/player/metadata/video/{$id}";
        $params = [
            'embedder' => 'https%3A%2F%2Fgo.medcom.com.pa%2F',
            'geo' => '1',
            'player-id' => 'xhq0f',
            'locale' => 'es',
            'dmV1st' => 'e5290302-bd86-4dcc-9733-92926e698c82',
            'dmTs' => '139192',
            'is_native_app' => '0',
            'app' => 'mark.va.gp',
            'dmSharingUrlLocation' => "https%3A%2F%2Fgo.medcom.com.pa%2F{$channel_name}{$id}"
        ];
        
        return $base_url . '?' . http_build_query($params);
    }
    
    /**
     * Extraer enlaces de calidades específicas
     */
    public function extractQualityLinks($m3u8_content) {
        $regex = '!https?://[^\s]+!';
        if (!preg_match_all($regex, $m3u8_content, $links)) {
            return false;
        }
        return $links[0];
    }
    
    /**
     * Obtener mapeo de calidades
     */
    public function getQualityMap() {
        return [
            240 => 2,
            380 => 4, 
            480 => 0,
            720 => 6,
            1080 => 8
        ];
    }
    
    /**
     * Enviar headers apropiados para M3U8
     */
    public function sendM3U8Headers($filename) {
        header("Content-Type: application/x-mpegURL");
        header("Content-disposition: inline; filename={$filename}");
        header("Cache-Control: no-cache, no-store, must-revalidate");
        header("Pragma: no-cache");
        header("Expires: 0");
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, OPTIONS");
        header("Access-Control-Allow-Headers: Content-Type");
    }
    
    /**
     * Enviar respuesta de error
     */
    public function sendError($code, $message) {
        http_response_code($code);
        header('Content-Type: text/plain; charset=utf-8');
        echo $message;
        exit;
    }
    
    /**
     * Procesar solicitud de stream
     */
    public function processStreamRequest($id, $channel_name) {
        try {
            $m3u8_content = $this->extractVideoUrls($id, $channel_name);
            
            if ($m3u8_content === false) {
                $this->sendError(503, "Error: No se pudo obtener el contenido del stream de {$channel_name}. Intente nuevamente en unos momentos.");
            }
            
            $quality = isset($_GET["calidad"]) ? intval($_GET["calidad"]) : null;
            
            if (!$quality) {
                // Devolver playlist completa
                $this->sendM3U8Headers("{$channel_name}.m3u8");
                echo $m3u8_content;
            } else {
                // Redirigir a calidad específica
                $this->redirectToQuality($m3u8_content, $quality);
            }
            
        } catch (Exception $e) {
            error_log("Error en processStreamRequest para {$channel_name}: " . $e->getMessage());
            $this->sendError(500, "Error interno del servidor. Intente nuevamente.");
        }
    }
    
    /**
     * Redirigir a calidad específica
     */
    private function redirectToQuality($m3u8_content, $quality) {
        $links = $this->extractQualityLinks($m3u8_content);
        
        if ($links === false) {
            $this->sendError(503, "Error: No se pudieron extraer los enlaces de calidad.");
        }
        
        $quality_map = $this->getQualityMap();
        
        if (isset($quality_map[$quality]) && isset($links[$quality_map[$quality]])) {
            header("Location: " . $links[$quality_map[$quality]]);
        } else {
            // Fallback a la primera calidad disponible
            if (!empty($links)) {
                header("Location: " . $links[0]);
            } else {
                $this->sendError(503, "Error: No hay enlaces de calidad disponibles.");
            }
        }
    }
}
