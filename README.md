# MEDCOM Stream Proxy - Versión Mejorada

## Descripción
Sistema mejorado para extraer y servir streams de video de los canales de MEDCOM (OyeTV, RPC, Telemetro) desde Dailymotion con manejo robusto de errores y reconexión automática.

## Problemas Solucionados

### Problemas Originales:
1. **Conexiones que se caían frecuentemente** - Sin manejo de errores ni reintentos
2. **Demora en reconectarse** - No había mecanismo de reconexión automática
3. **Timeouts indefinidos** - Las conexiones podían colgarse sin límite de tiempo
4. **Código duplicado** - Los 3 archivos tenían lógica idéntica
5. **Bug en telemetro.php** - Variable incorrecta en el switch de calidades
6. **Headers mal ubicados** - Se enviaban después del contenido

### Mejoras Implementadas:

#### 1. **<PERSON><PERSON><PERSON>rro<PERSON>**
- Validación completa de respuestas HTTP
- Verificación de formato de datos recibidos
- Mensajes de error informativos para el usuario
- Logging de errores para debugging

#### 2. **Sistema de Reintentos Inteligente**
- **5 intentos automáticos** por defecto (configurable)
- **Backoff exponencial**: 1s, 2s, 4s, 8s, 16s entre reintentos
- Reintentos tanto para metadata como para contenido M3U8

#### 3. **Timeouts Configurables**
- **15 segundos de timeout** por defecto (configurable)
- Evita conexiones colgadas indefinidamente
- Optimizado para conexiones lentas

#### 4. **Arquitectura Modular**
- **stream_helper.php**: Biblioteca común con toda la lógica
- Archivos individuales simplificados (solo configuración)
- Fácil mantenimiento y actualizaciones centralizadas

#### 5. **Headers HTTP Mejorados**
- Headers enviados antes del contenido
- Cache control apropiado para streams en vivo
- CORS habilitado para compatibilidad cross-origin
- User-Agent realista para evitar bloqueos

## Estructura de Archivos

```
├── stream_helper.php    # Biblioteca común (lógica principal)
├── oyetv.php           # Proxy para OyeTV (simplificado)
├── rpc.php             # Proxy para RPC (simplificado)
├── telemetro.php       # Proxy para Telemetro (simplificado)
└── README.md           # Esta documentación
```

## Uso

### Acceso Básico (Playlist completa)
```
http://tu-servidor/oyetv.php
http://tu-servidor/rpc.php
http://tu-servidor/telemetro.php
```

### Acceso por Calidad Específica
```
http://tu-servidor/oyetv.php?calidad=720
http://tu-servidor/rpc.php?calidad=1080
http://tu-servidor/telemetro.php?calidad=480
```

### Calidades Disponibles
- `240` - Calidad baja
- `380` - Calidad media-baja
- `480` - Calidad media (SD)
- `720` - Calidad alta (HD)
- `1080` - Calidad máxima (Full HD)

## Configuración Avanzada

### Personalizar Reintentos y Timeouts
Editar en cada archivo PHP:

```php
$streamHelper = new StreamHelper(
    $maxRetries = 5,    // Número de reintentos
    $timeout = 15       // Timeout en segundos
);
```

### Configuraciones Recomendadas por Escenario:

#### Conexión Rápida y Estable:
```php
$streamHelper = new StreamHelper(3, 10);
```

#### Conexión Lenta o Inestable:
```php
$streamHelper = new StreamHelper(7, 20);
```

#### Servidor con Alta Carga:
```php
$streamHelper = new StreamHelper(3, 8);
```

## Monitoreo y Debugging

### Logs de Error
Los errores se registran en el log de PHP. Para habilitar logging:

```php
// En stream_helper.php, cambiar:
ini_set('log_errors', 1);
ini_set('error_log', '/path/to/error.log');
```

### Códigos de Respuesta HTTP
- `200` - Éxito
- `503` - Servicio no disponible (problema con el stream)
- `500` - Error interno del servidor

## Ventajas de la Nueva Versión

1. **Mayor Estabilidad**: Reintentos automáticos reducen interrupciones
2. **Mejor Performance**: Timeouts evitan conexiones colgadas
3. **Fácil Mantenimiento**: Código centralizado en una biblioteca
4. **Mejor UX**: Mensajes de error claros para usuarios
5. **Escalabilidad**: Configuración flexible por canal
6. **Debugging**: Logging detallado para troubleshooting

## Compatibilidad

- **PHP 7.0+** requerido
- Compatible con todos los reproductores que soporten M3U8/HLS
- Funciona con VLC, navegadores modernos, apps móviles, etc.

## Próximas Mejoras Sugeridas

1. **Cache de URLs**: Cachear URLs M3U8 por unos minutos
2. **Health Check**: Endpoint para verificar estado de canales
3. **Métricas**: Contadores de requests y errores
4. **Load Balancing**: Soporte para múltiples servidores
5. **API REST**: Endpoints JSON para integración con apps
